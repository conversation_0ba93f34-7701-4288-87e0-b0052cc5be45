import { defineConfig, presetAttributify, presetIcons, presetTypography, presetUno } from 'unocss'
import presetChinese from 'unocss-preset-chinese'
import presetEase from 'unocss-preset-ease'

export default defineConfig({
  theme: {
    colors: {
      primary: 'var(--primary-color)',
      'primary-7': 'var(--primary-color-7)',
      'primary-6': 'var(--primary-color-6)',
      'primary-5': 'var(--primary-color-5)',
      'primary-4': 'var(--primary-color-4)',
      'primary-3': 'var(--primary-color-3)',
      'primary-2': 'var(--primary-color-2)',
      'primary-1': 'var(--primary-color-1)',

      success: 'var(--success-color)',
      'success-7': 'var(--success-color-7)',
      'success-6': 'var(--success-color-6)',
      'success-5': 'var(--success-color-5)',
      'success-4': 'var(--success-color-4)',
      'success-3': 'var(--success-color-3)',
      'success-2': 'var(--success-color-2)',
      'success-1': 'var(--success-color-1)',

      warning: 'var(--warning-color)',
      'warning-7': 'var(--warning-color-7)',
      'warning-6': 'var(--warning-color-6)',
      'warning-5': 'var(--warning-color-5)',
      'warning-4': 'var(--warning-color-4)',
      'warning-3': 'var(--warning-color-3)',
      'warning-2': 'var(--warning-color-2)',
      'warning-1': 'var(--warning-color-1)',

      error: 'var(--error-color)',
      'error-7': 'var(--error-color-7)',
      'error-6': 'var(--error-color-6)',
      'error-5': 'var(--error-color-5)',
      'error-4': 'var(--error-color-4)',
      'error-3': 'var(--error-color-3)',
      'error-2': 'var(--error-color-2)',
      'error-1': 'var(--error-color-1)',

      info: 'var(--info-color)',
      'info-7': 'var(--info-color-7)',
      'info-6': 'var(--info-color-6)',
      'info-5': 'var(--info-color-5)',
      'info-4': 'var(--info-color-4)',
      'info-3': 'var(--info-color-3)',
      'info-2': 'var(--info-color-2)',
      'info-1': 'var(--info-color-1)',
    },
    fontFamily: {
      sans: ['PingFang SC', 'Microsoft YaHei', 'sans-serif'],
    },
  },
  presets: [
    presetUno(),
    presetAttributify(),
    presetChinese(),
    presetEase(),
    presetTypography(),
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
  ],
  shortcuts: [
    ['f1', 'flex-1 overflow-hidden'],
    ['primary', 'color-primary hover:color-primary hover:opacity-50'],
    ['error', 'color-error hover:color-error hover:opacity-50'],
    ['success', 'color-success hover:color-success hover:opacity-50'],
    ['warning', 'color-warning hover:color-warning hover:opacity-50'],
    ['info', 'color-info hover:color-info hover:opacity-50'],
    ['flex-center', 'flex items-center justify-center'],
    ['flex-between', 'flex items-center justify-between'],
    ['flex-end', 'flex items-end justify-between'],
  ],
})
