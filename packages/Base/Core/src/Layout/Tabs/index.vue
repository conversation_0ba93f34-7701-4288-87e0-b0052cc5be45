<template>
  <div class="tabs-container">
    <div class="tabs-wrapper">
      <div v-for="tab in tabs" :key="tab.path" :class="{ active: activeTab === tab.path }" class="tab-item" @click="handleTabClick(tab)">
        <div class="tab-content">
          <span class="tab-title">{{ tab.title }}</span>
          <div class="tab-actions">
            <ReloadOutlined class="action-icon reload" @click.stop="handleRefresh(tab)" />
            <CloseOutlined v-if="closeable && tabs.length > 1" class="action-icon close" @click.stop="handleClose(tab)" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CloseOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { appConfig } from '@idmy/core'
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getTabsCacheKey, refresh<PERSON>ey } from '../index'

defineProps({
  closeable: {
    type: Boolean,
    default: true,
  },
})

interface Tab {
  title: string
  path: string
}

const route = useRoute()
const router = useRouter()
const tabs = useCache<Tab[]>(getTabsCacheKey(), [])
const activeTab = ref('')

// 监听路由变化，添加新标签
watch(
  () => route.path,
  newPath => {
    // 如果是重定向路由，不添加到标签页
    if (newPath === '/redirect') return

    const matched = route.matched.find(r => r.path === newPath)
    if (matched?.meta?.title) {
      const newTab = {
        title: matched.meta.title as string,
        path: newPath,
      }

      if (!tabs.value.some(tab => tab.path === newPath)) {
        tabs.value.push(newTab)
      }
      activeTab.value = newPath
    }
  },
  { immediate: true }
)

// 点击标签
const handleTabClick = (tab: Tab) => {
  if (tab.path !== route.path) {
    router.push(tab.path)
  }
}

// 关闭标签
const handleClose = (tab: Tab) => {
  const index = tabs.value.findIndex(t => t.path === tab.path)
  if (index > -1) {
    tabs.value.splice(index, 1)
    // 如果关闭的是当前标签，则跳转到前一个标签
    if (tab.path === route.path) {
      const nextTab = tabs.value[index] || tabs.value[index - 1]
      if (nextTab) {
        router.push(nextTab.path)
      }
    }
  }
}

const handleRefresh = async (tab: Tab) => {
  if (tab.path === route.path) {
    refreshKey.value++
  }
}
</script>

<style lang="less" scoped>
.tabs-container {
  background: #fff;
  padding: 0;
  height: 32px;
  border-bottom: 1px solid #e0e0e0;
}

.tabs-wrapper {
  display: flex;
  gap: 0;
  overflow-x: auto;
  height: 100%;

  &::-webkit-scrollbar {
    height: 0;
    width: 0;
  }
}

.tab-item {
  position: relative;
  height: 100%;
  min-width: 120px;
  max-width: 200px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0 12px;
  border-right: 1px solid #e0e0e0;
  background: #f5f5f5;
  transition: all 0.2s ease;

  .tab-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 0;
    color: #666;
    height: 100%;
  }

  &:hover {
    background: #ebebeb;

    .tab-actions {
      opacity: 1;

      .reload {
        opacity: 1;
      }
    }
  }

  &.active {
    background: #fff;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: var(--primary-color);
    }

    .tab-content {
      color: #333;
    }

    .tab-actions {
      opacity: 1;

      .reload {
        opacity: 0;
      }
    }

    &:hover .tab-actions .reload {
      opacity: 1;
    }
  }

  &:not(.active) {
    .tab-actions .reload {
      display: none; /* Hide reload completely for inactive tabs */
    }
  }
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
  font-size: 13px;
  line-height: 32px;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
  height: 100%;
  padding: 0 4px;
  margin-right: -12px;

  .reload {
    opacity: 0;
  }
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;

  &:hover {
    background: rgba(0, 0, 0, 0.06);
  }

  &.reload {
    color: #666;
  }

  &.close {
    color: #666;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
