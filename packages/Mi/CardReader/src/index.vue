<script setup lang="ts">
import { ref } from 'vue'
import { <PERSON><PERSON> as <PERSON>utton, Modal as AModal, notification } from 'ant-design-vue'
import CardReaderModal from './CardReaderModal.vue'
import type { PropType } from 'vue'

const props = defineProps({
  // 业务类型ID
  businessTypeId: {
    type: String,
    required: true
  },
  // 医疗类型
  medicalType: {
    type: Number,
    required: true
  },
  // 就诊ID
  visitId: {
    type: Number,
    default: undefined
  },
  // 其他参数
  params: {
    type: Object,
    default: () => ({})
  },
  // 请求接口
  hostUrl: {
    type: String,
    default: ''
  },
  // 是否调用医保
  isGetMI: {
    type: Boolean,
    default: true
  },
  // 医保调用后 是否选择医保险种
  isMIOpenSelect: {
    type: Boolean,
    default: true
  },
  // 医保调用后 是否显示慢特信息
  isMIShowMT: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['success'])

// 是否显示读卡弹窗
const visible = ref(false)
// 引用 CardReaderModal 组件
const cardReaderModalRef = ref(null)

// 点击读卡按钮
const handleClick = () => {
  console.log(props)
  // 校验必填参数
  if (!props.businessTypeId) {
    notification.error({
      message: '参数缺失',
      description: '请在读卡组件中配置参数 business-type-id（业务类型ID）'
    })
    return
  }
  if (!props.medicalType) {
    notification.error({
      message: '参数缺失',
      description: '请在读卡组件中配置参数 medical-type（医疗类型）'
    })
    return
  }

  visible.value = true
}

// 读卡成功回调
const onSuccess = (data: any) => {
  visible.value = false
  emit('success', data)
}

// 关闭弹窗
const onCancel = () => {
  visible.value = false
}

// 确认险种回调
const onConfirmInsuType = (data: any) => {
  visible.value = false
  console.log(data)
  emit('success', data)
}

// 点击确认险种按钮
const handleConfirmClick = () => {
  if (cardReaderModalRef.value) {
    cardReaderModalRef.value.confirmInsuType()
  }
}

defineExpose({
  handleClick
})
</script>

<template>
  <div class="card-reader-button">
    <!-- 读卡按钮 -->
    <a-button type="primary" @click="handleClick">
      读卡
    </a-button>

    <!-- 读卡弹窗 -->
    <a-modal
      v-model:visible="visible"
      title="读卡"
      width="1000px"
      :maskClosable="false"
      :destroyOnClose="true"
      :closable="false"
      @cancel="onCancel"
    >
      <card-reader-modal
        v-if="visible"
        ref="cardReaderModalRef"
        :business-type-id="props.businessTypeId"
        :medical-type="props.medicalType"
        :visit-id="props.visitId"
        :params="params"
        :host-url="hostUrl"
        :is-get-m-i="isGetMI"
        :is-m-i-open-select="isMIOpenSelect"
        :is-m-i-show-m-t="isMIShowMT"
        @success="onSuccess"
        @cancel="onCancel"
        @confirm-insu-type="onConfirmInsuType"
      />
      <template #footer>
        <div style="text-align: right;">
          <a-button type="primary" @click="handleConfirmClick">
            {{ cardReaderModalRef?.insuinfoModel?.list?.length > 0 ? '确认险种' : '关闭' }}
          </a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
.card-reader-button {
  display: inline-block;
}
</style>
