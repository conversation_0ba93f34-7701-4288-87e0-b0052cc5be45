<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { Bar<PERSON>hart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  Canvas<PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

interface Props {
  data: {
    charge: number
    refund: number
    actualCharge: number
  }
}

const props = defineProps<Props>()

// 计算图表配置
const chartOption = computed(() => ({
  title: {
    text: '收费概览',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}: ¥${data.value.toLocaleString()}`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['收费', '退费', '实收'],
    axisLabel: {
      fontSize: 12
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => {
        if (value >= 10000) {
          return (value / 10000).toFixed(1) + '万'
        }
        return value.toLocaleString()
      }
    }
  },
  series: [
    {
      name: '金额',
      type: 'bar',
      data: [
        {
          value: props.data.charge,
          itemStyle: { color: '#009b9b' }
        },
        {
          value: Math.abs(props.data.refund),
          itemStyle: { color: '#ff4d4f' }
        },
        {
          value: props.data.actualCharge,
          itemStyle: { color: '#52c41a' }
        }
      ],
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          const value = params.value
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value.toLocaleString()
        }
      }
    }
  ]
}))
</script>

<template>
  <div class="revenue-chart">
    <VChart :option="chartOption" style="height: 200px;" />
  </div>
</template>

<style scoped lang="less">
.revenue-chart {
  width: 100%;
  height: 200px;
}
</style>
