<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { Bar<PERSON>hart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  CanvasRenderer,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

interface Props {
  data: {
    pricingForms: number
    outpatientFee: number
    inpatientFee: number
    registrationFee: number
    unsubmitted: number
    submitted: number
  }
}

const props = defineProps<Props>()

// 计算图表配置
const chartOption = computed(() => ({
  title: {
    text: '业务统计',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: (params: any) => {
      const data = params[0]
      const unit = data.name.includes('费用') ? '元' : '单'
      return `${data.name}: ${data.value.toLocaleString()}${unit}`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['划价单', '门诊费用', '住院费用', '挂号费用', '未交账', '已交账'],
    axisLabel: {
      fontSize: 12,
      rotate: 45
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '数量/金额',
      position: 'left',
      axisLabel: {
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value.toLocaleString()
        }
      }
    }
  ],
  series: [
    {
      name: '业务数据',
      type: 'bar',
      data: [
        {
          value: props.data.pricingForms,
          itemStyle: { color: '#1890ff' }
        },
        {
          value: props.data.outpatientFee,
          itemStyle: { color: '#52c41a' }
        },
        {
          value: props.data.inpatientFee,
          itemStyle: { color: '#faad14' }
        },
        {
          value: props.data.registrationFee,
          itemStyle: { color: '#722ed1' }
        },
        {
          value: props.data.unsubmitted,
          itemStyle: { color: '#f5222d' }
        },
        {
          value: props.data.submitted,
          itemStyle: { color: '#13c2c2' }
        }
      ],
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          const value = params.value
          if (value === 0) return ''
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value.toLocaleString()
        }
      }
    }
  ]
}))
</script>

<template>
  <div class="business-chart">
    <VChart :option="chartOption" style="height: 300px;" />
  </div>
</template>

<style scoped lang="less">
.business-chart {
  width: 100%;
  height: 300px;
}
</style>
