<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pie<PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

interface Props {
  data: {
    insurance: number
    pooledFund: number
    personalAccount: number
    mutualAid: number
    selfPay: number
  }
}

const props = defineProps<Props>()

// 计算图表配置
const chartOption = computed(() => {
  const total = props.data.insurance + props.data.selfPay
  
  return {
    title: {
      text: '医保费用分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percent = ((params.value / total) * 100).toFixed(1)
        return `${params.name}: ¥${params.value.toLocaleString()} (${percent}%)`
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '费用分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: props.data.insurance,
            name: '医保',
            itemStyle: { color: '#1890ff' }
          },
          {
            value: props.data.pooledFund,
            name: '统筹',
            itemStyle: { color: '#52c41a' }
          },
          {
            value: props.data.personalAccount,
            name: '个账',
            itemStyle: { color: '#faad14' }
          },
          {
            value: props.data.mutualAid,
            name: '共济',
            itemStyle: { color: '#722ed1' }
          },
          {
            value: props.data.selfPay,
            name: '自费',
            itemStyle: { color: '#f5222d' }
          }
        ].filter(item => item.value > 0) // 只显示有值的项目
      }
    ]
  }
})
</script>

<template>
  <div class="insurance-chart">
    <VChart :option="chartOption" style="height: 300px;" />
  </div>
</template>

<style scoped lang="less">
.insurance-chart {
  width: 100%;
  height: 300px;
}
</style>
