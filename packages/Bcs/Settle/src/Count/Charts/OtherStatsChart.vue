<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, <PERSON>auge<PERSON><PERSON> } from 'echarts/charts'
import { TitleComponent, TooltipComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { Row, Col, Statistic } from 'ant-design-vue'

// 注册必要的组件
use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  GridComponent
])

interface Props {
  data: {
    pricingForms: number
    unsubmitted: number
    submitted: number
    prepayment: number
  }
}

const props = defineProps<Props>()

// 划价单数图表配置
const pricingFormsOption = computed(() => ({
  title: {
    text: '划价单数',
    left: 'center',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    formatter: () => `划价单数: ${props.data.pricingForms}单`
  },
  series: [
    {
      type: 'gauge',
      radius: '80%',
      min: 0,
      max: Math.max(props.data.pricingForms * 1.2, 100),
      splitNumber: 4,
      axisLine: {
        lineStyle: {
          width: 8,
          color: [
            [0.6, '#52c41a'],
            [0.8, '#faad14'],
            [1, '#f5222d']
          ]
        }
      },
      pointer: {
        itemStyle: {
          color: '#1890ff'
        }
      },
      axisTick: {
        distance: -15,
        length: 5,
        lineStyle: {
          color: '#fff',
          width: 1
        }
      },
      splitLine: {
        distance: -20,
        length: 15,
        lineStyle: {
          color: '#fff',
          width: 2
        }
      },
      axisLabel: {
        color: 'auto',
        distance: 25,
        fontSize: 10
      },
      detail: {
        valueAnimation: true,
        formatter: '{value}',
        color: 'auto',
        fontSize: 16,
        offsetCenter: [0, '70%']
      },
      data: [
        {
          value: props.data.pricingForms
        }
      ]
    }
  ]
}))

// 未交账数图表配置
const unsubmittedOption = computed(() => ({
  title: {
    text: '未交账数',
    left: 'center',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    formatter: () => `未交账数: ${props.data.unsubmitted}单`
  },
  series: [
    {
      type: 'gauge',
      radius: '80%',
      min: 0,
      max: Math.max(props.data.unsubmitted * 1.2, 50),
      splitNumber: 4,
      axisLine: {
        lineStyle: {
          width: 8,
          color: [
            [0.3, '#52c41a'],
            [0.7, '#faad14'],
            [1, '#f5222d']
          ]
        }
      },
      pointer: {
        itemStyle: {
          color: '#ff4d4f'
        }
      },
      axisTick: {
        distance: -15,
        length: 5,
        lineStyle: {
          color: '#fff',
          width: 1
        }
      },
      splitLine: {
        distance: -20,
        length: 15,
        lineStyle: {
          color: '#fff',
          width: 2
        }
      },
      axisLabel: {
        color: 'auto',
        distance: 25,
        fontSize: 10
      },
      detail: {
        valueAnimation: true,
        formatter: '{value}',
        color: 'auto',
        fontSize: 16,
        offsetCenter: [0, '70%']
      },
      data: [
        {
          value: props.data.unsubmitted
        }
      ]
    }
  ]
}))

// 已交账数图表配置
const submittedOption = computed(() => ({
  title: {
    text: '已交账数',
    left: 'center',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    formatter: () => `已交账数: ${props.data.submitted}单`
  },
  series: [
    {
      type: 'gauge',
      radius: '80%',
      min: 0,
      max: Math.max(props.data.submitted * 1.2, 20),
      splitNumber: 4,
      axisLine: {
        lineStyle: {
          width: 8,
          color: [
            [0.8, '#52c41a'],
            [0.9, '#faad14'],
            [1, '#f5222d']
          ]
        }
      },
      pointer: {
        itemStyle: {
          color: '#13c2c2'
        }
      },
      axisTick: {
        distance: -15,
        length: 5,
        lineStyle: {
          color: '#fff',
          width: 1
        }
      },
      splitLine: {
        distance: -20,
        length: 15,
        lineStyle: {
          color: '#fff',
          width: 2
        }
      },
      axisLabel: {
        color: 'auto',
        distance: 25,
        fontSize: 10
      },
      detail: {
        valueAnimation: true,
        formatter: '{value}',
        color: 'auto',
        fontSize: 16,
        offsetCenter: [0, '70%']
      },
      data: [
        {
          value: props.data.submitted
        }
      ]
    }
  ]
}))

// 预交金图表配置
const prepaymentOption = computed(() => ({
  title: {
    text: '预交金',
    left: 'center',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    formatter: () => `预交金: ¥${props.data.prepayment.toLocaleString()}`
  },
  grid: {
    left: '10%',
    right: '10%',
    bottom: '20%',
    top: '30%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['预交金'],
    axisLabel: {
      fontSize: 10
    },
    show: false
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 10,
      formatter: (value: number) => {
        if (value >= 10000) {
          return (value / 10000).toFixed(1) + '万'
        }
        return value.toLocaleString()
      }
    }
  },
  series: [
    {
      name: '预交金',
      type: 'bar',
      data: [
        {
          value: props.data.prepayment,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#87d068' },
                { offset: 1, color: '#52c41a' }
              ]
            }
          }
        }
      ],
      barWidth: '40%',
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          const value = params.value
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value.toLocaleString()
        },
        fontSize: 12
      }
    }
  ]
}))
</script>

<template>
  <div class="other-stats-chart">
    <Row :gutter="4">
      <Col :span="6">
        <div class="chart-container">
          <VChart :option="pricingFormsOption" style="height: 100px;" />
        </div>
      </Col>
      <Col :span="6">
        <div class="chart-container">
          <VChart :option="unsubmittedOption" style="height: 100px;" />
        </div>
      </Col>
      <Col :span="6">
        <div class="chart-container">
          <VChart :option="submittedOption" style="height: 100px;" />
        </div>
      </Col>
      <Col :span="6">
        <div class="chart-container">
          <VChart :option="prepaymentOption" style="height: 100px;" />
        </div>
      </Col>
    </Row>
  </div>
</template>

<style scoped lang="less">
.other-stats-chart {
  width: 100%;

  .chart-container {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
