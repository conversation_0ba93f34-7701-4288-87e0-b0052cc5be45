<script setup lang="ts">
import { LineChart } from 'echarts/charts'
import { GridComponent, LegendComponent, TitleComponent, TooltipComponent } from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed } from 'vue'
import VChart from 'vue-echarts'

// 注册必要的组件
use([Canvas<PERSON>ender<PERSON>, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])

interface TrendData {
  date: string
  charge: number
  refund: number
  actualCharge: number
}

interface Props {
  data: TrendData[]
}

const props = defineProps<Props>()

// 计算图表配置
const chartOption = computed(() => ({
  title: {
    text: '收费趋势',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal',
    },
  },
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      let result = `${params[0].axisValue}<br/>`
      params.forEach((item: any) => {
        result += `${item.seriesName}: ¥${item.value.toLocaleString()}<br/>`
      })
      return result
    },
  },
  legend: {
    data: ['收费', '退费', '实收'],
    top: 30,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '15%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: props.data.map(item => item.date),
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => {
        if (value >= 10000) {
          return (value / 10000).toFixed(1) + '万'
        }
        return value.toLocaleString()
      },
    },
  },
  series: [
    {
      name: '收费',
      type: 'line',
      data: props.data.map(item => item.charge),
      itemStyle: { color: '#009b9b' },
      smooth: true,
    },
    {
      name: '退费',
      type: 'line',
      data: props.data.map(item => Math.abs(item.refund)),
      itemStyle: { color: '#ff4d4f' },
      smooth: true,
    },
    {
      name: '实收',
      type: 'line',
      data: props.data.map(item => item.actualCharge),
      itemStyle: { color: '#52c41a' },
      smooth: true,
    },
  ],
}))
</script>

<template>
  <VChart :option="chartOption" />
</template>
