import { Data, getCache, useLoading } from '@idmy/core'
import { Currents } from '@mh-base/core'
import { READ_CARD_SUCCESS_EVENT } from '@mh-mi/card-reader'
import { getCash, getPatientIdByIdcertNo, getUserCode, getVisit, updateBillTimes } from '@mh-bcs/util'
import { cloneDeep } from 'lodash-es'
import { UnwrapRef } from 'vue'
import Charge from './Charge.vue'


export const isRedBill = (bill: Data) => Boolean(bill.blueBillId)

export const isSelfFee = (insuranceTypeId): boolean => insuranceTypeId === cfg.tenant.selfPaidInsuranceTypeId || !insuranceTypeId

export function useSettle(selected: { keys: number[]; rows: Data[] }) {
  const columns: any = [
    { align: 'left', dataIndex: 'billId', title: '项目', minWidth: 120, ellipsis: true },
    { align: 'center', dataIndex: 'execDeptName', width: 90, title: '执行科室', ellipsis: true },
    { align: 'right', dataIndex: 'amount', title: '费用', width: 80 },
  ]

  const showTip = ref(false)

  const refreshShowTip = () => {
    showTip.value = getCache('refundUnprocessed', true)
  }

  //region 表格高度
  const tableHeight = ref()
  const handleResize = (): void => {
    tableHeight.value = window.innerHeight - 140 - (showTip.value ? 48 : 0)
  }

  watch(
    () => showTip.value,
    () => handleResize()
  )

  onMounted((): void => {
    handleResize()
    window.addEventListener('resize', handleResize)
  })
  onUnmounted(() => window.removeEventListener('resize', handleResize))
  //endregion

  const onRemoveBill = (billId: number) => {
    selected.keys = selected.keys.filter(key => key !== billId)
    selected.rows = selected.rows.filter(row => row.bseqid !== billId)
  }

  return {
    refreshShowTip,
    showTip,
    columns,
    onRemoveBill,
    tableHeight,
  }
}

export function useReadCard(ok: (patientId: number) => void) {
  const card = ref()
  const readCardRef = ref()

  async function clearCard() {
    card.value = {}
    readCardRef.value?.clearState()
  }

  const params = computed(() => ({
    business_type_id: '104',
    medical_type: '11',
    org_id: Currents.tenantId,
    user_id: Currents.id,
  }))

  function selfAdaptionReadCard(val: Data): void {
    if (val.miData?.baseinfo?.psn_cert_type && !val.readCard?.psn_cert_type) {
      val.readCard.psn_cert_type = val.miData.baseinfo.psn_cert_type
      val.readCard.psn_cert_no = val.miData.baseinfo.certno
      val.readCard.psn_no = val.miData.baseinfo?.psn_no
      val.readCard.psn_name = val.miData.baseinfo.psn_name
    }
  }

  emitter.on(READ_CARD_SUCCESS_EVENT, async (val: Data) => {
    selfAdaptionReadCard(val)
    card.value = val
    if (val.readCard?.psn_cert_type) {
      const data = await getPatientIdByIdcertNo(1, val.readCard?.psn_cert_no)
      if (data) {
        ok(data)
      } else {
        throw new Error('未找到该患者')
      }
    }
  })

  nextTick(() => clearCard())

  return {
    card,
    clearCard,
    params,
    readCardRef,
  }
}

export function useTimesEdit(onLoad: any, rows: Ref<UnwrapRef<Data[]>>) {
  const editableData: UnwrapRef<Record<string, any>> = reactive({})
  const edit = (billId: number) => {
    editableData[billId] = cloneDeep(rows.value.filter((item: any): boolean => billId === item.bseqid)[0])
  }
  const save = async (billId: number): Promise<void> => {
    const times = editableData[billId].times
    delete editableData[billId]
    await updateBillTimes(billId, times)
    await onLoad(false)
  }
  return {
    edit,
    editableData,
    save,
  }
}

//region 用户码表恢复
export function useUserCode(options: Data) {
  const [onUserCode] = useLoading(async () => {
    const { cashId, visitId } = await getUserCode()
    if (cashId) {
      const { cashType, payer, insuranceTypeId } = await getCash(cashId)
      if (cashType) {
        if (options.cashType !== cashType) {
          return
        }
      }
      let name = payer
      if (visitId) {
        const { ageOfYears, genderName, patientId } = await getVisit(visitId)
        name = `${name} ${genderName} ${ageOfYears}岁 ${patientId ? '' : '未建档'}`
      }
      const showMiFund = !isSelfFee(insuranceTypeId) && Boolean(visitId)
      Modal.open({
        component: Charge,
        width: 4,
        title: `收费「${name}」`,
        props: { cashType: options.cashType, cashId, visitId, showMiFund, autoFinish: false },
        closable: false,
        onClose: async () => {
          options.clearCard?.()
          await options.onLoad?.()
        },
      })
    }
  })
  return {
    onUserCode,
  }
}

//endregion
