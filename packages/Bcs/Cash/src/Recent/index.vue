<script lang="ts" setup>
import { View } from '@idmy/antd'
import { Format } from '@idmy/core'
import { pageCash } from '@mh-bcs/util'
import { InputSearch, Popover, Space, Spin } from 'ant-design-vue'
import { isNil } from 'lodash-es'

const emit = defineEmits(['update:modelValue'])

defineProps({
  height: {
    type: Number,
    default: 0,
  },
})

const data = ref([])
const [onLoad] = useLoading(async () => {
  const { list } = await pageCash({
    params: {
      cashTypes: ['OUTPATIENT'],
      statuses: ['OK'],
      isCheck: 'false',
    },
    sorts: ['createdAt', 'desc'],
    pageSize: 20,
  })
  data.value = list
}, true)

const selected = defineModel()
const keyword = ref('')
const filter = computed(() => data.value.filter(row => row.payer.includes(keyword.value)))
</script>

<template>
  <View title="最近结算">
    <InputSearch v-model:value="keyword" placeholder="患者姓名" />
    <div class="patient-list" :style="{ maxHeight: height - 32 + 'px', height: height - 32 + 'px' }">
      <div flex-center h-full v-if="loading">
        <Spin />
      </div>
      <template v-else>
        <template v-for="row in filter">
          <Popover v-model:visible="row.visible" :arrow="false" placement="left" :mouseEnterDelay="0">
            <template #content>
              <Space @click="row.visible = false">
                <slot name="op" :row="row" :onLoad="onLoad" />
              </Space>
            </template>
            <div :key="row.billId" class="patient-item" @click="selected = row.cashId">
              <div class="f1">
                <div class="patient-name">
                  <div>{{ row.payer }}</div>
                  <div text-12px>
                    <Format type="Date" :value="row.createdAt" params="DD日HH:mm" />
                  </div>
                </div>
                <div class="patient-details">
                  <span class="patient-age">
                    <template v-if="!isNil(row.ageOfYears)">{{ row.ageOfYears }} 岁</template>
                  </span>
                  <Format type="Currency" :value="row.amount" />
                </div>
              </div>
            </div>
          </Popover>
        </template>
      </template>
    </div>
  </View>
</template>
<style lang="less" scoped>
.patient-list {
  overflow-y: auto;
  border: solid 1px #ddd;
  border-top: none;
}

.patient-item {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: var(--primary-color-2);
  }
}

.patient-item-selected {
  background-color: var(--primary-color-5);
  color: white !important;

  .patient-details {
    color: #eee !important;
  }

  &:hover {
    background-color: var(--primary-color-5);
  }
}

.patient-name {
  font-size: 14px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
}

.patient-details {
  font-size: 12px;
  color: #888;
  display: flex;
  justify-content: space-between;
}
</style>
