<script setup lang="ts">
import { themeConfig } from '@mh-base/core'
import { CardReaderButton, READ_CARD_CLICK_EVENT, READ_CARD_SUCCESS_EVENT, readCardCurrentProps } from '@mh-mi/card-reader'
import { ConfigProvider } from 'ant-design-vue'
import locale from 'ant-design-vue/es/locale/zh_CN'

const onReadCardSuccess = (...args) => {
  emitter.emit(READ_CARD_SUCCESS_EVENT, ...args)
}

const readCardRef = ref()
emitter.on(READ_CARD_CLICK_EVENT, () => {
  readCardRef.value.handleClick()
})
</script>

<template>
  <ConfigProvider :theme="themeConfig" :locale="locale">
    <CardReaderButton ref="readCardRef" v-show="false" v-bind="readCardCurrentProps" @success="onReadCardSuccess" />
    <RouterView />
  </ConfigProvider>
</template>
