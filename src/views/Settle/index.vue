<script setup lang="ts">
import { OpenCashDetail, RecentCash } from '@mh-bcs/cash'
import { OpenFullRefund, OpenRefund, OpenUndo, SettleList } from '@mh-bcs/settle'
import { Button, Col, Row } from 'ant-design-vue'

const height = ref()
</script>

<template>
  <Row :gutter="8">
    <Col :span="21">
      <SettleList v-model:height="height" />
    </Col>
    <Col :span="3">
      <RecentCash style="margin-right: -16px" :height="height">
        <template v-slot:op="{ row, onLoad }">
          <OpenFullRefund :data="row" @ok="onLoad" :component="Button" type="primary" size="small" />
          <OpenRefund :data="row" @ok="onLoad" :component="Button" type="primary" size="small" />
          <OpenUndo :data="row" @ok="onLoad" :component="Button" type="primary" size="small" />
          <OpenCashDetail :component="Button" type="primary" :data="row" :onLoad="onLoad" position="left" size="small">详情</OpenCashDetail>
        </template>
      </RecentCash>
    </Col>
  </Row>
</template>
