import { setCache } from '@idmy/core'
import { DefaultLayout, getTabsCacheKey } from '@mh-base/core'
import { CashPage } from '@mh-bcs/cash'
import { DepositIndex } from '@mh-bcs/deposit'
import { Count } from '@mh-bcs/settle'
import { READ_CARD_CLICK_EVENT } from '@mh-mi/card-reader'
import SettleList from '@v/Settle/index.vue'
import { Button } from 'ant-design-vue'

DefaultLayout.props = {
  ...DefaultLayout.props,
  nav: {
    type: Object,
    default: () => {
      return h('div', [h(Button, { ghost: true, shape: 'round', size: 'small', class: 'hover:bg-white!', onClick: () => emitter.emit(READ_CARD_CLICK_EVENT) }, '读卡')])
    },
  },
  closeable: {
    type: Boolean,
    default: false,
  },
  showSider: {
    type: Boolean,
    default: false,
  },
  contentClass: {
    type: String,
    default: '',
  },
}

setCache(getTabsCacheKey(), [
  { title: '我的收费', path: '/bcs/Settle/count' },
  { title: '门诊收费', path: '/bcs/Settle/list' },
  { title: '收费记录', path: '/bcs/Cash/page' },
  { title: '交账记录', path: '/bcs/Cash/page2' },
  { title: '发票记录', path: '/bcs/Cash/page3' },
])

export default {
  path: '/bcs',
  meta: {
    title: '收费工作站',
  },
  component: DefaultLayout,
  children: [
    {
      path: '/bcs/Settle/count',
      component: Count,
      meta: {
        title: '我的收费',
      },
    },
    {
      path: '/bcs/Settle/list',
      component: SettleList,
      meta: {
        title: '门诊收费',
        cache: true,
      },
    },
    {
      path: '/bcs/Settle/list2',
      component: SettleList,
      meta: {
        title: '住院收费',
      },
    },
    {
      path: '/bcs/Deposit/index',
      component: DepositIndex,
      meta: {
        title: '预交金',
      },
    },
    {
      path: '/bcs/Cash/page',
      component: CashPage,
      name: 'list',
      meta: {
        title: '收费记录',
        cache: true,
      },
      props: {},
    },
    {
      path: '/bcs/Cash/page2',
      component: CashPage,
      meta: {
        title: '交账记录',
      },
      props: {},
    },
    {
      path: '/bcs/Cash/page3',
      component: CashPage,
      meta: {
        title: '发票记录',
      },
      props: {},
    },
  ],
}
