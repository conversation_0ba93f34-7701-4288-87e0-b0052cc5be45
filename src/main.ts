import './config'
import DmyAntd from '@idmy/antd'
import { initialized } from '@idmy/core'
import Antd from 'ant-design-vue'
import { createApp } from 'vue'
import App from './App.vue'
import routerFn from './router'
import 'uno.css'

const app = createApp(App)
app.use(Antd)
app.use(DmyAntd)
initialized(app, () => {
  const router = routerFn()
  app.use(router)
  router.isReady().then(async () => {
    await sleep(500)
    app.mount('#app')
  })
})
